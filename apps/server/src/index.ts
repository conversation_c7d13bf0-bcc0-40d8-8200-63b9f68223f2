import "dotenv/config";
import { OpenAPIHandler } from "@orpc/openapi/fetch";
import { OpenAPIReferencePlugin } from "@orpc/openapi/plugins";
import { onError } from "@orpc/server";
import { RPC<PERSON>andler } from "@orpc/server/fetch";
import { ZodToJsonSchemaConverter } from "@orpc/zod/zod4";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { validateEnv } from "./config/env";
import { handleStripeWebhook } from "./handlers/webhook";
import { auth } from "./lib/auth";
import { createContext } from "./lib/context";
import { appRouter } from "./routers/index";

const app = new Hono();

if (process.env.NODE_ENV !== "test") {
	console.log("Validating environment variables...");
	validateEnv();
}

// Global error handlers for debugging
process.on("unhandledRejection", (reason, promise) => {
	const timestamp = new Date().toISOString();
	console.error(
		`[${timestamp}] [UNHANDLED_REJECTION] Unhandled Promise Rejection:`,
		reason,
	);
	console.error(`[${timestamp}] [UNHANDLED_REJECTION] Promise:`, promise);
});

process.on("uncaughtException", (error) => {
	const timestamp = new Date().toISOString();
	console.error(
		`[${timestamp}] [UNCAUGHT_EXCEPTION] Uncaught Exception:`,
		error,
	);
});

app.use(logger());
app.use(
	"/*",
	cors({
		origin: process.env.CORS_ORIGIN || "",
		allowMethods: ["GET", "POST", "OPTIONS"],
		allowHeaders: ["Content-Type", "Authorization"],
		credentials: true,
	}),
);

app.on(["POST", "GET"], "/api/auth/**", async (c) => {
	const timestamp = new Date().toISOString();
	const url = new URL(c.req.url);
	console.log(`[${timestamp}] [AUTH_REQUEST] ${c.req.method} ${url.pathname}`);

	// Log request body for POST requests (excluding sensitive data)
	if (c.req.method === "POST") {
		try {
			const body = await c.req.clone().json();
			const sanitizedBody = { ...body };
			if (sanitizedBody.password) sanitizedBody.password = "[REDACTED]";
			console.log(
				`[${timestamp}] [AUTH_REQUEST] Body:`,
				JSON.stringify(sanitizedBody, null, 2),
			);
		} catch {
			console.log(`[${timestamp}] [AUTH_REQUEST] Could not parse body as JSON`);
		}
	}

	try {
		const response = await auth.handler(c.req.raw);
		console.log(`[${timestamp}] [AUTH_RESPONSE] Status: ${response.status}`);

		// Log response body for error status codes
		if (response.status >= 400) {
			try {
				const responseClone = response.clone();
				const responseBody = await responseClone.text();
				console.log(`[${timestamp}] [AUTH_RESPONSE] Error body:`, responseBody);
			} catch {
				console.log(
					`[${timestamp}] [AUTH_RESPONSE] Could not read response body`,
				);
			}
		}

		return response;
	} catch (error) {
		console.error(`[${timestamp}] [AUTH_ERROR] Error in auth handler:`, error);
		throw error;
	}
});

// Stripe webhook endpoint - must be before the catch-all middleware
app.post("/webhooks/stripe", handleStripeWebhook);

export const apiHandler = new OpenAPIHandler(appRouter, {
	plugins: [
		new OpenAPIReferencePlugin({
			schemaConverters: [new ZodToJsonSchemaConverter()],
		}),
	],
	interceptors: [
		onError((error) => {
			const timestamp = new Date().toISOString();
			console.error(`[${timestamp}] [API_ERROR] Error occurred:`, error);

			// Additional structured logging for debugging
			if (error instanceof Error) {
				console.error(`[${timestamp}] [API_ERROR] Error details:`, {
					name: error.name,
					message: error.message,
					stack: error.stack,
				});
			}
		}),
	],
});

export const rpcHandler = new RPCHandler(appRouter, {
	interceptors: [
		onError((error) => {
			const timestamp = new Date().toISOString();
			console.error(`[${timestamp}] [RPC_ERROR] Error occurred:`, error);

			// Additional structured logging for debugging
			if (error instanceof Error) {
				console.error(`[${timestamp}] [RPC_ERROR] Error details:`, {
					name: error.name,
					message: error.message,
					stack: error.stack,
				});
			}
		}),
	],
});

app.use("/*", async (c, next) => {
	const context = await createContext({ context: c });

	const rpcResult = await rpcHandler.handle(c.req.raw, {
		prefix: "/rpc",
		context: context,
	});

	if (rpcResult.matched) {
		return c.newResponse(rpcResult.response.body, rpcResult.response);
	}

	const apiResult = await apiHandler.handle(c.req.raw, {
		prefix: "/api",
		context: context,
	});

	if (apiResult.matched) {
		return c.newResponse(apiResult.response.body, apiResult.response);
	}

	await next();
});

app.get("/", (c) => {
	return c.text("OK");
});

export default app;
