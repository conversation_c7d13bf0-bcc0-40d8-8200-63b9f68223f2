import "dotenv/config";
import { OpenAPIHandler } from "@orpc/openapi/fetch";
import { OpenAPIReferencePlugin } from "@orpc/openapi/plugins";
import { onError } from "@orpc/server";
import { RPCHandler } from "@orpc/server/fetch";
import { ZodToJsonSchemaConverter } from "@orpc/zod/zod4";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { validateEnv } from "./config/env";
import { handleStripeWebhook } from "./handlers/webhook";
import { auth } from "./lib/auth";
import { createContext } from "./lib/context";
import { Logger, logger } from "./lib/logger";
import { honoLoggingMiddleware, logError } from "./lib/logging-middleware";
import { appRouter } from "./routers/index";

const app = new Hono();

if (process.env.NODE_ENV !== "test") {
	console.log("Validating environment variables...");
	validateEnv();
}

// Global error handlers for debugging
const globalLogger = logger.child("GLOBAL");

process.on("unhandledRejection", (reason, promise) => {
	globalLogger.error("Unhandled Promise Rejection", { reason, promise });
});

process.on("uncaughtException", (error) => {
	globalLogger.error("Uncaught Exception", {}, error);
	process.exit(1); // Exit on uncaught exceptions
});

app.use(honoLoggingMiddleware);
app.use(
	"/*",
	cors({
		origin: process.env.CORS_ORIGIN || "",
		allowMethods: ["GET", "POST", "OPTIONS"],
		allowHeaders: ["Content-Type", "Authorization"],
		credentials: true,
	}),
);

app.on(["POST", "GET"], "/api/auth/**", async (c) => {
	const correlationId = Logger.generateCorrelationId();
	const authLogger = logger
		.withCorrelationId(correlationId)
		.child("AUTH_HANDLER");

	const url = new URL(c.req.url);
	const method = c.req.method;
	const path = url.pathname;

	authLogger.info("Auth request started", { method, path });

	// Log request body for POST requests (excluding sensitive data)
	if (method === "POST") {
		try {
			const bodyText = await c.req.text();
			c.req = new Request(c.req.url, {
				method: c.req.method,
				headers: c.req.headers,
				body: bodyText,
			});

			const body = JSON.parse(bodyText);
			authLogger.debug("Auth request body received", { body });
		} catch {
			authLogger.debug("Could not parse auth request body as JSON");
		}
	}

	try {
		const response = await auth.handler(c.req.raw);
		authLogger.info("Auth response", { status: response.status });

		// Log response body for error status codes
		if (response.status >= 400) {
			try {
				const responseClone = response.clone();
				const responseBody = await responseClone.text();
				authLogger.error("Auth error response", {
					status: response.status,
					body: responseBody,
				});
			} catch {
				authLogger.warn("Could not read auth response body");
			}
		}

		return response;
	} catch (error) {
		logError("AUTH_HANDLER", "Error in auth handler", error, { method, path });
		throw error;
	}
});

// Stripe webhook endpoint - must be before the catch-all middleware
app.post("/webhooks/stripe", handleStripeWebhook);

export const apiHandler = new OpenAPIHandler(appRouter, {
	plugins: [
		new OpenAPIReferencePlugin({
			schemaConverters: [new ZodToJsonSchemaConverter()],
		}),
	],
	interceptors: [
		onError((error) => {
			logError("API_HANDLER", "API error occurred", error);
		}),
	],
});

export const rpcHandler = new RPCHandler(appRouter, {
	interceptors: [
		onError((error) => {
			logError("RPC_HANDLER", "RPC error occurred", error);
		}),
	],
});

app.use("/*", async (c, next) => {
	const context = await createContext({ context: c });

	const rpcResult = await rpcHandler.handle(c.req.raw, {
		prefix: "/rpc",
		context: context,
	});

	if (rpcResult.matched) {
		return c.newResponse(rpcResult.response.body, rpcResult.response);
	}

	const apiResult = await apiHandler.handle(c.req.raw, {
		prefix: "/api",
		context: context,
	});

	if (apiResult.matched) {
		return c.newResponse(apiResult.response.body, apiResult.response);
	}

	await next();
});

app.get("/", (c) => {
	return c.text("OK");
});

export default app;
