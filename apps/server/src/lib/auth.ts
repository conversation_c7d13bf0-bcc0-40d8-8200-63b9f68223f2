import { expo } from "@better-auth/expo";

import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "@/db";
import { betterAuthLogger, createAuthLogger, logError } from "./logger";

export const auth = betterAuth({
	database: prismaAdapter(prisma, {
		provider: "postgresql",
	}),
	trustedOrigins: [process.env.CORS_ORIGIN || "", "mybettertapp://", "exp://"],
	emailAndPassword: {
		enabled: true,
		autoSignIn: true,
	},
	logger: {
		disabled: false,
		disableColors: false,
		level: "debug",
		log: betterAuthLogger.log,
	},
	user: {
		additionalFields: {
			role: {
				type: "string",
				required: false,
				input: false,
				returned: true,
			},
			token: {
				type: "string",
				required: false,
				input: false, // Changed from true to false - token is only for validation, not storage
			},
		},
	},
	//Database hooks for first user bootstrap and invitation integration
	databaseHooks: {
		user: {
			create: {
				before: async (user, ctx) => {
					const authLogger = createAuthLogger("USER_CREATE_BEFORE");

					authLogger.info("Starting user creation process", {
						userEmail: user.email,
						userName: user.name,
						contextBody: ctx?.body,
					});

					try {
						// Check if this is the first user (bootstrap admin)
						const userCount = await prisma.user.count();
						authLogger.debug("Checked user count", { userCount });

						if (userCount === 0) {
							authLogger.info("Creating first user (SUPER_ADMIN)");
							const firstUserData = {
								...user,
								role: "SUPER_ADMIN",
								isActive: true,
								emailVerified: true, // First user is auto-verified
							};
							authLogger.debug("First user data prepared", {
								role: firstUserData.role,
								isActive: firstUserData.isActive,
								emailVerified: firstUserData.emailVerified,
							});
							return {
								data: firstUserData,
							};
						}

						// For subsequent users, check if they have an invitation token
						const token = ctx?.body.token;
						authLogger.debug("Invitation token check", {
							tokenProvided: !!token,
							tokenLength: token?.length,
						});

						if (!token) {
							authLogger.error(
								"No invitation token provided for subsequent user",
							);
							throw new Error(
								"Invitation token is required for user registration",
							);
						}

						// Check if user already exists before looking up invitation
						authLogger.debug("Checking if user already exists", {
							email: user.email,
						});
						const existingUser = await prisma.user.findUnique({
							where: { email: user.email },
						});

						if (existingUser) {
							authLogger.error("User already exists", { email: user.email });
							throw new Error("User with this email already exists");
						}

						// Look up the invitation
						authLogger.debug("Looking up invitation", { token });
						const invitation = await prisma.invitation.findUnique({
							where: { token: token },
						});

						authLogger.debug("Invitation lookup result", {
							found: !!invitation,
							invitationId: invitation?.id,
							email: invitation?.email,
							status: invitation?.status,
							role: invitation?.role,
							expiresAt: invitation?.expiresAt,
						});

						if (!invitation) {
							authLogger.error("Invalid invitation token", { token });
							throw new Error("Invalid invitation token");
						}

						// Check invitation status
						if (invitation.status !== "PENDING") {
							authLogger.error("Invitation status is not PENDING", {
								status: invitation.status,
								invitationId: invitation.id,
							});

							// If invitation is already accepted, check if user already exists
							if (invitation.status === "ACCEPTED") {
								authLogger.debug(
									"Checking if user already exists for accepted invitation",
								);
								const existingUser = await prisma.user.findUnique({
									where: { email: invitation.email },
								});

								if (existingUser) {
									authLogger.error("User already exists for this invitation", {
										email: invitation.email,
										existingUserId: existingUser.id,
									});
									throw new Error("User with this email already exists");
								}
							}

							throw new Error(
								`Invitation is ${invitation.status.toLowerCase()}`,
							);
						}

						// Check if invitation is expired
						if (invitation.expiresAt < new Date()) {
							authLogger.error("Invitation has expired", {
								expiresAt: invitation.expiresAt,
								now: new Date(),
							});
							throw new Error("Invitation has expired");
						}

						// Check if email matches
						if (invitation.email !== user.email) {
							authLogger.error("Email mismatch", {
								invitationEmail: invitation.email,
								userEmail: user.email,
							});
							throw new Error("Email does not match invitation");
						}

						authLogger.info("Invitation validation successful", {
							invitationId: invitation.id,
							role: invitation.role,
						});

						const userData = {
							...user,
							role: invitation.role || "USER",
							isActive: true,
							emailVerified: true, // Users created through invitation are auto-verified
						};
						authLogger.debug("Final user data prepared", {
							role: userData.role,
							isActive: userData.isActive,
							emailVerified: userData.emailVerified,
						});

						return {
							data: userData,
						};
					} catch (error) {
						logError(
							"USER_CREATE_BEFORE",
							"Error in user creation before hook",
							error,
							{
								userEmail: user.email,
								contextBody: ctx?.body,
							},
						);
						throw error;
					}
				},
				after: async (user, ctx) => {
					const authLogger = createAuthLogger("USER_CREATE_AFTER");

					authLogger.info("Starting post-creation process", {
						userId: user.id,
						userEmail: user.email,
						contextBody: ctx?.body,
					});

					try {
						// create audit log for user creation
						authLogger.debug("Creating audit log");
						await prisma.auditLog.create({
							data: {
								action: "USER_CREATED",
								userId: user.id,
								userEmail: user.email,
							},
						});
						authLogger.debug("Audit log created successfully");

						const token = ctx?.body.token;
						if (!token) {
							authLogger.debug("No invitation token to process");
							return;
						}

						// Mark invitation as accepted
						authLogger.info("Updating invitation status to ACCEPTED", {
							token,
						});
						const updatedInvitation = await prisma.invitation.update({
							where: { token: token },
							data: {
								status: "ACCEPTED",
								acceptedAt: new Date(),
								acceptedBy: user.id,
							},
						});
						authLogger.debug("Invitation updated successfully", {
							invitationId: updatedInvitation.id,
							status: updatedInvitation.status,
							acceptedAt: updatedInvitation.acceptedAt,
						});
						authLogger.info("Post-creation process completed successfully");
					} catch (error) {
						logError(
							"USER_CREATE_AFTER",
							"Error in user creation after hook",
							error,
							{
								userId: user.id,
								userEmail: user.email,
								contextBody: ctx?.body,
							},
						);
						throw error;
					}
				},
			},
		},
	},

	advanced: {
		defaultCookieAttributes: {
			sameSite: "none",
			secure: true,
			httpOnly: true,
		},
	},
	plugins: [expo()],
});
