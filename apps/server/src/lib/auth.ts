import { expo } from "@better-auth/expo";

import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "@/db";

export const auth = betterAuth({
	database: prismaAdapter(prisma, {
		provider: "postgresql",
	}),
	trustedOrigins: [process.env.CORS_ORIGIN || "", "mybettertapp://", "exp://"],
	emailAndPassword: {
		enabled: true,
		autoSignIn: true,
	},
	logger: {
		disabled: false,
		disableColors: false,
		level: "debug", // Changed from "error" to "debug" to capture more details
		log: (level, message, ...args) => {
			// Enhanced logging with timestamp and structured data
			const timestamp = new Date().toISOString();
			const logData = args.length > 0 ? JSON.stringify(args, null, 2) : "";
			console.log(
				`[${timestamp}] [BETTER_AUTH:${level.toUpperCase()}] ${message}${logData ? `\nData: ${logData}` : ""}`,
			);
		},
	},
	user: {
		additionalFields: {
			role: {
				type: "string",
				required: false,
				input: false,
				returned: true,
			},
			token: {
				type: "string",
				required: false,
				input: false, // Changed from true to false - token is only for validation, not storage
			},
		},
	},
	//Database hooks for first user bootstrap and invitation integration
	databaseHooks: {
		user: {
			create: {
				before: async (user, ctx) => {
					const timestamp = new Date().toISOString();
					console.log(
						`[${timestamp}] [USER_CREATE_BEFORE] Starting user creation process`,
					);
					console.log(
						`[${timestamp}] [USER_CREATE_BEFORE] User data:`,
						JSON.stringify(user, null, 2),
					);
					console.log(
						`[${timestamp}] [USER_CREATE_BEFORE] Context body:`,
						JSON.stringify(ctx?.body, null, 2),
					);

					try {
						// Check if this is the first user (bootstrap admin)
						const userCount = await prisma.user.count();
						console.log(
							`[${timestamp}] [USER_CREATE_BEFORE] Current user count: ${userCount}`,
						);

						if (userCount === 0) {
							
							const firstUserData = {
								...user,
								role: "SUPER_ADMIN",
								isActive: true,
								emailVerified: true, // First user is auto-verified
							};
							console.log(
								`[${timestamp}] [USER_CREATE_BEFORE] First user data:`,
								JSON.stringify(firstUserData, null, 2),
							);
							return {
								data: firstUserData,
							};
						}

						// For subsequent users, check if they have an invitation token
						const token = ctx?.body.token;
						console.log(
							`[${timestamp}] [USER_CREATE_BEFORE] Invitation token provided: ${token ? "YES" : "NO"}`,
						);

						if (!token) {
							console.log(
								`[${timestamp}] [USER_CREATE_BEFORE] ERROR: No invitation token provided for subsequent user`,
							);
							throw new Error(
								"Invitation token is required for user registration",
							);
						}

						// Check if user already exists before looking up invitation
						console.log(
							`[${timestamp}] [USER_CREATE_BEFORE] Checking if user already exists: ${user.email}`,
						);
						const existingUser = await prisma.user.findUnique({
							where: { email: user.email },
						});

						if (existingUser) {
							console.log(
								`[${timestamp}] [USER_CREATE_BEFORE] ERROR: User already exists with email: ${user.email}`,
							);
							throw new Error("User with this email already exists");
						}

						// Look up the invitation
						console.log(
							`[${timestamp}] [USER_CREATE_BEFORE] Looking up invitation with token: ${token}`,
						);
						const invitation = await prisma.invitation.findUnique({
							where: { token: token },
						});

						console.log(
							`[${timestamp}] [USER_CREATE_BEFORE] Invitation lookup result:`,
							JSON.stringify(invitation, null, 2),
						);

						if (!invitation) {
							console.log(
								`[${timestamp}] [USER_CREATE_BEFORE] ERROR: Invalid invitation token: ${token}`,
							);
							throw new Error("Invalid invitation token");
						}

						// Check invitation status
						if (invitation.status !== "PENDING") {
							console.log(
								`[${timestamp}] [USER_CREATE_BEFORE] ERROR: Invitation status is not PENDING: ${invitation.status}`,
							);

							// If invitation is already accepted, check if user already exists
							if (invitation.status === "ACCEPTED") {
								console.log(
									`[${timestamp}] [USER_CREATE_BEFORE] Checking if user already exists for accepted invitation`,
								);
								const existingUser = await prisma.user.findUnique({
									where: { email: invitation.email },
								});

								if (existingUser) {
									console.log(
										`[${timestamp}] [USER_CREATE_BEFORE] ERROR: User already exists for this invitation`,
									);
									throw new Error("User with this email already exists");
								}
							}

							throw new Error(
								`Invitation is ${invitation.status.toLowerCase()}`,
							);
						}

						// Check if invitation is expired
						if (invitation.expiresAt < new Date()) {
							console.log(
								`[${timestamp}] [USER_CREATE_BEFORE] ERROR: Invitation expired at: ${invitation.expiresAt}`,
							);
							throw new Error("Invitation has expired");
						}

						// Check if email matches
						if (invitation.email !== user.email) {
							console.log(
								`[${timestamp}] [USER_CREATE_BEFORE] ERROR: Email mismatch - invitation: ${invitation.email}, user: ${user.email}`,
							);
							throw new Error("Email does not match invitation");
						}

						console.log(
							`[${timestamp}] [USER_CREATE_BEFORE] Invitation validation successful`,
						);

						const userData = {
							...user,
							role: invitation.role || "USER",
							isActive: true,
							emailVerified: true, // Users created through invitation are auto-verified
						};
						console.log(
							`[${timestamp}] [USER_CREATE_BEFORE] Final user data:`,
							JSON.stringify(userData, null, 2),
						);

						return {
							data: userData,
						};
					} catch (error) {
						console.error(`[${timestamp}] [USER_CREATE_BEFORE] ERROR:`, error);
						throw error;
					}
				},
				after: async (user, ctx) => {
					const timestamp = new Date().toISOString();
					console.log(
						`[${timestamp}] [USER_CREATE_AFTER] Starting post-creation process`,
					);
					console.log(
						`[${timestamp}] [USER_CREATE_AFTER] Created user:`,
						JSON.stringify(user, null, 2),
					);
					console.log(
						`[${timestamp}] [USER_CREATE_AFTER] Context body:`,
						JSON.stringify(ctx?.body, null, 2),
					);

					try {
						// create audit log for user creation
						console.log(
							`[${timestamp}] [USER_CREATE_AFTER] Creating audit log`,
						);
						await prisma.auditLog.create({
							data: {
								action: "USER_CREATED",
								userId: user.id,
								userEmail: user.email,
							},
						});
						console.log(
							`[${timestamp}] [USER_CREATE_AFTER] Audit log created successfully`,
						);

						const token = ctx?.body.token;
						if (!token) {
							console.log(
								`[${timestamp}] [USER_CREATE_AFTER] No invitation token to process`,
							);
							return;
						}

						// Mark invitation as accepted
						console.log(
							`[${timestamp}] [USER_CREATE_AFTER] Updating invitation status to ACCEPTED for token: ${token}`,
						);
						const updatedInvitation = await prisma.invitation.update({
							where: { token: token },
							data: {
								status: "ACCEPTED",
								acceptedAt: new Date(),
								acceptedBy: user.id,
							},
						});
						console.log(
							`[${timestamp}] [USER_CREATE_AFTER] Invitation updated:`,
							JSON.stringify(updatedInvitation, null, 2),
						);
						console.log(
							`[${timestamp}] [USER_CREATE_AFTER] Post-creation process completed successfully`,
						);
					} catch (error) {
						console.error(`[${timestamp}] [USER_CREATE_AFTER] ERROR:`, error);
						throw error;
					}
				},
			},
		},
	},

	advanced: {
		defaultCookieAttributes: {
			sameSite: "none",
			secure: true,
			httpOnly: true,
		},
	},
	plugins: [expo()],
});
