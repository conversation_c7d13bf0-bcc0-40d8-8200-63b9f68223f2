import { type ORPCError, os } from "@orpc/server";
import type { Context } from "./context";
import { logger } from "./logger";
import { convertPrismaError, isPrismaError } from "./prisma-error-handler";

/**
 * ORPC middleware that automatically converts Prisma errors to appropriate ORPC errors
 * This middleware should be applied globally to catch all Prisma errors across the application
 */
export const prismaErrorMiddleware = os
	.$context<Context>()
	.middleware(async ({ next }) => {
		try {
			// Execute the next middleware/handler
			return await next();
		} catch (error) {
			// Check if this is a Prisma error we can convert
			const conversionResult = convertPrismaError(error);

			if (conversionResult) {
				// Enhanced logging for debugging purposes
				const prismaLogger = logger.child("PRISMA_ERROR_MIDDLEWARE");

				prismaLogger.error("Converted Prisma error", {
					originalError: {
						name: conversionResult.originalError.name,
						message: conversionResult.originalError.message,
						stack: conversionResult.originalError.stack,
						...(isPrismaError(conversionResult.originalError) && {
							code: (conversionResult.originalError as any).code,
							meta: (conversionResult.originalError as any).meta,
						}),
					},
					convertedTo: {
						code: conversionResult.orpcError.code,
						message: conversionResult.orpcError.message,
						data: conversionResult.orpcError.data,
					},
				});

				// Throw the converted ORPC error
				throw conversionResult.orpcError;
			}

			// If it's not a Prisma error, re-throw the original error
			// This allows other error handling mechanisms to process it
			throw error;
		}
	});

/**
 * Enhanced base procedure with Prisma error handling
 * This creates a base procedure that automatically handles Prisma errors
 */
export const baseProcedureWithPrismaErrorHandling = os
	.$context<Context>()
	.use(prismaErrorMiddleware);

/**
 * Utility function to wrap any ORPC procedure with Prisma error handling
 * Use this if you want to selectively apply Prisma error handling to specific procedures
 *
 * @param procedure - The ORPC procedure to wrap
 * @returns The procedure with Prisma error handling applied
 */
export function withPrismaErrorHandling<
	T extends ReturnType<typeof os.$context<Context>>,
>(procedure: T) {
	return procedure.use(prismaErrorMiddleware);
}

/**
 * Type-safe error handling for specific Prisma error codes
 * This can be used in handlers when you need to handle specific Prisma errors differently
 *
 * @param fn - The function to execute that might throw Prisma errors
 * @param errorHandlers - Object mapping Prisma error codes to custom handlers
 * @returns The result of the function or throws the appropriate error
 */
export async function handlePrismaErrors<T>(
	fn: () => Promise<T>,
	errorHandlers?: {
		[K in string]?: (error: any) => ORPCError<any, any> | void;
	},
): Promise<T> {
	try {
		return await fn();
	} catch (error) {
		// Check for custom error handlers first
		if (errorHandlers && isPrismaError(error)) {
			const customHandler = errorHandlers[(error as any).code];
			if (customHandler) {
				const customError = customHandler(error);
				if (customError) {
					throw customError;
				}
			}
		}

		// Fall back to automatic conversion
		const conversionResult = convertPrismaError(error);
		if (conversionResult) {
			throw conversionResult.orpcError;
		}

		// Re-throw if not a Prisma error
		throw error;
	}
}

/**
 * Decorator function for handlers that need Prisma error handling
 * This is useful when you want to apply error handling to a specific handler function
 *
 * @param handler - The handler function to wrap
 * @returns The wrapped handler with Prisma error handling
 */
export function withPrismaErrorHandlingDecorator<
	TInput,
	TOutput,
	TContext extends Context,
>(handler: (params: { input: TInput; context: TContext }) => Promise<TOutput>) {
	return async (params: {
		input: TInput;
		context: TContext;
	}): Promise<TOutput> => {
		try {
			return await handler(params);
		} catch (error) {
			const conversionResult = convertPrismaError(error);
			if (conversionResult) {
				throw conversionResult.orpcError;
			}
			throw error;
		}
	};
}
