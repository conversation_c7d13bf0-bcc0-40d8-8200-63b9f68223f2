import type { Context as Hono<PERSON>ontext } from "hono";
import { os } from "@orpc/server";
import { logger, Logger } from "./logger";
import type { Context } from "./context";

/**
 * ORPC logging middleware for automatic request/response logging
 */
export const orpcLoggingMiddleware = os
	.$context<Context>()
	.middleware(async ({ next, context, input }) => {
		const correlationId = Logger.generateCorrelationId();
		const requestLogger = logger.withCorrelationId(correlationId).child("ORPC");

		const timer = requestLogger.timer("Request processing");

		try {
			requestLogger.info("Request started", {
				input: input ? JSON.stringify(input) : undefined,
				userId: context.session?.user?.id,
				userEmail: context.session?.user?.email,
			});

			const result = await next();

			timer();
			requestLogger.info("Request completed successfully");

			return result;
		} catch (error) {
			timer();
			requestLogger.error(
				"Request failed",
				{
					input: input ? JSON.stringify(input) : undefined,
					userId: context.session?.user?.id,
					userEmail: context.session?.user?.email,
				},
				error instanceof Error ? error : new Error(String(error)),
			);
			throw error;
		}
	});

/**
 * Hono middleware for HTTP request/response logging
 */
export const honoLoggingMiddleware = async (
	c: HonoContext,
	next: () => Promise<void>,
) => {
	const correlationId = Logger.generateCorrelationId();
	const requestLogger = logger.withCorrelationId(correlationId).child("HTTP");

	const url = new URL(c.req.url);
	const method = c.req.method;
	const path = url.pathname;

	const timer = requestLogger.timer(`${method} ${path}`);

	try {
		// Log request
		const requestData: Record<string, unknown> = {
			method,
			path,
			query: Object.fromEntries(url.searchParams),
			userAgent: c.req.header("user-agent"),
			ip: c.req.header("x-forwarded-for") || c.req.header("x-real-ip"),
		};

		// Log request body for POST/PUT/PATCH requests
		if (["POST", "PUT", "PATCH"].includes(method)) {
			try {
				const body = await c.req.clone().json();
				requestData.body = body;
			} catch {
				// Body is not JSON or empty
				requestData.body = "[Non-JSON body]";
			}
		}

		requestLogger.info("HTTP request started", requestData);

		// Store correlation ID in context for downstream use
		c.set("correlationId", correlationId);
		c.set("requestLogger", requestLogger);

		await next();

		// Log response
		timer();
		requestLogger.info("HTTP request completed", {
			status: c.res.status,
			statusText: c.res.statusText,
		});
	} catch (error) {
		timer();
		requestLogger.error(
			"HTTP request failed",
			{
				method,
				path,
				status: c.res?.status,
			},
			error instanceof Error ? error : new Error(String(error)),
		);
		throw error;
	}
};

/**
 * Better Auth logging adapter
 */
export const betterAuthLogger = {
	log: (level: string, message: string, ...args: unknown[]) => {
		const authLogger = logger.child("BETTER_AUTH");
		const data = args.length > 0 ? { args } : undefined;

		switch (level.toLowerCase()) {
			case "debug":
				authLogger.debug(message, data);
				break;
			case "info":
				authLogger.info(message, data);
				break;
			case "warn":
				authLogger.warn(message, data);
				break;
			case "error":
				authLogger.error(message, data);
				break;
			default:
				authLogger.info(`[${level.toUpperCase()}] ${message}`, data);
		}
	},
};

/**
 * Database operation logging helper
 */
export const createDatabaseLogger = (operation: string) => {
	return logger.child(`DB_${operation.toUpperCase()}`);
};

/**
 * Authentication flow logging helper
 */
export const createAuthLogger = (flow: string) => {
	return logger.child(`AUTH_${flow.toUpperCase()}`);
};

/**
 * Error logging helper with context
 */
export const logError = (
	context: string,
	message: string,
	error: unknown,
	additionalData?: Record<string, unknown>,
) => {
	const errorLogger = logger.child(context);
	const errorObj = error instanceof Error ? error : new Error(String(error));

	errorLogger.error(message, additionalData, errorObj);
};

/**
 * Performance logging helper
 */
export const logPerformance = (
	operation: string,
	duration: number,
	additionalData?: Record<string, unknown>,
) => {
	const perfLogger = logger.child("PERFORMANCE");
	perfLogger.info(`Operation completed: ${operation}`, {
		duration: `${duration}ms`,
		...additionalData,
	});
};

/**
 * Audit logging helper for security events
 */
export const logAudit = (
	action: string,
	userId?: string,
	additionalData?: Record<string, unknown>,
) => {
	const auditLogger = logger.child("AUDIT");
	auditLogger.info(`Audit: ${action}`, {
		userId,
		timestamp: new Date().toISOString(),
		...additionalData,
	});
};

/**
 * Validation error logging helper
 */
export const logValidationError = (
	context: string,
	errors: unknown,
	input?: unknown,
) => {
	const validationLogger = logger.child("VALIDATION");
	validationLogger.warn(`Validation failed in ${context}`, {
		errors,
		input,
	});
};

/**
 * External service logging helper
 */
export const createExternalServiceLogger = (serviceName: string) => {
	return logger.child(`EXTERNAL_${serviceName.toUpperCase()}`);
};

/**
 * Request correlation helper - extracts correlation ID from Hono context
 */
export const getCorrelationId = (c: HonoContext): string | undefined => {
	return c.get("correlationId");
};

/**
 * Request logger helper - gets the request logger from Hono context
 */
export const getRequestLogger = (c: HonoContext): Logger | undefined => {
	return c.get("requestLogger");
};
