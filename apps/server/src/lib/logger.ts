import { randomUUID } from "node:crypto";

/**
 * Log levels in order of severity
 */
export enum LogLevel {
	DEBUG = 0,
	INFO = 1,
	WARN = 2,
	ERROR = 3,
}

/**
 * Log level names for display
 */
const LOG_LEVEL_NAMES: Record<LogLevel, string> = {
	[LogLevel.DEBUG]: "DEBUG",
	[LogLevel.INFO]: "INFO",
	[LogLevel.WARN]: "WARN",
	[LogLevel.ERROR]: "ERROR",
};

/**
 * Configuration for the logger
 */
interface LoggerConfig {
	level: LogLevel;
	enableColors: boolean;
	enableTimestamps: boolean;
	enableCorrelationIds: boolean;
	redactSensitiveData: boolean;
	prettyPrint: boolean;
}

/**
 * Log entry structure
 */
interface LogEntry {
	timestamp: string;
	level: LogLevel;
	message: string;
	data?: Record<string, unknown>;
	correlationId?: string;
	context?: string;
	error?: {
		name: string;
		message: string;
		stack?: string;
	};
}

/**
 * Sensitive data patterns to redact
 */
const SENSITIVE_PATTERNS = [
	/password/i,
	/token/i,
	/secret/i,
	/key/i,
	/auth/i,
	/credential/i,
	/bearer/i,
];

/**
 * ANSI color codes for console output
 */
const COLORS = {
	reset: "\x1b[0m",
	bright: "\x1b[1m",
	dim: "\x1b[2m",
	red: "\x1b[31m",
	green: "\x1b[32m",
	yellow: "\x1b[33m",
	blue: "\x1b[34m",
	magenta: "\x1b[35m",
	cyan: "\x1b[36m",
	white: "\x1b[37m",
	gray: "\x1b[90m",
};

/**
 * Color mapping for log levels
 */
const LEVEL_COLORS: Record<LogLevel, string> = {
	[LogLevel.DEBUG]: COLORS.gray,
	[LogLevel.INFO]: COLORS.blue,
	[LogLevel.WARN]: COLORS.yellow,
	[LogLevel.ERROR]: COLORS.red,
};

/**
 * Centralized logging service
 */
class Logger {
	private config: LoggerConfig;
	private correlationId?: string;
	private context?: string;

	constructor(config?: Partial<LoggerConfig>) {
		this.config = {
			level: this.getLogLevelFromEnv(),
			enableColors: process.env.NODE_ENV !== "production",
			enableTimestamps: true,
			enableCorrelationIds: true,
			redactSensitiveData: true,
			prettyPrint: process.env.NODE_ENV !== "production",
			...config,
		};
	}

	/**
	 * Get log level from environment variables
	 */
	private getLogLevelFromEnv(): LogLevel {
		const envLevel = process.env.LOG_LEVEL?.toUpperCase();
		switch (envLevel) {
			case "DEBUG":
				return LogLevel.DEBUG;
			case "INFO":
				return LogLevel.INFO;
			case "WARN":
				return LogLevel.WARN;
			case "ERROR":
				return LogLevel.ERROR;
			default:
				return process.env.NODE_ENV === "production"
					? LogLevel.INFO
					: LogLevel.DEBUG;
		}
	}

	/**
	 * Create a child logger with context
	 */
	child(context: string, correlationId?: string): Logger {
		const childLogger = new Logger(this.config);
		childLogger.context = context;
		childLogger.correlationId = correlationId || this.correlationId;
		return childLogger;
	}

	/**
	 * Set correlation ID for request tracking
	 */
	withCorrelationId(correlationId: string): Logger {
		const newLogger = new Logger(this.config);
		newLogger.context = this.context;
		newLogger.correlationId = correlationId;
		return newLogger;
	}

	/**
	 * Start a performance timer
	 */
	startTimer(): {
		end: (message: string, data?: Record<string, unknown>) => void;
	} {
		const start = Date.now();
		return {
			end: (message: string, data?: Record<string, unknown>) => {
				const duration = Date.now() - start;
				this.info(message, { ...data, duration: `${duration}ms` });
			},
		};
	}

	/**
	 * Redact sensitive data from objects
	 */
	private redactSensitiveData(obj: unknown): unknown {
		if (!this.config.redactSensitiveData) {
			return obj;
		}

		if (typeof obj === "string") {
			return obj;
		}

		if (Array.isArray(obj)) {
			return obj.map((item) => this.redactSensitiveData(item));
		}

		if (obj && typeof obj === "object") {
			const redacted: Record<string, unknown> = {};
			for (const [key, value] of Object.entries(obj)) {
				const shouldRedact = SENSITIVE_PATTERNS.some((pattern) =>
					pattern.test(key),
				);
				redacted[key] = shouldRedact
					? "[REDACTED]"
					: this.redactSensitiveData(value);
			}
			return redacted;
		}

		return obj;
	}

	/**
	 * Format log entry for output
	 */
	private formatLogEntry(entry: LogEntry): string {
		const levelName = LOG_LEVEL_NAMES[entry.level];
		const color = this.config.enableColors ? LEVEL_COLORS[entry.level] : "";
		const reset = this.config.enableColors ? COLORS.reset : "";

		let output = "";

		// Timestamp
		if (this.config.enableTimestamps) {
			const timestampColor = this.config.enableColors ? COLORS.gray : "";
			output += `${timestampColor}[${entry.timestamp}]${reset} `;
		}

		// Level
		output += `${color}[${levelName}]${reset} `;

		// Context
		if (entry.context) {
			const contextColor = this.config.enableColors ? COLORS.cyan : "";
			output += `${contextColor}[${entry.context}]${reset} `;
		}

		// Correlation ID
		if (entry.correlationId && this.config.enableCorrelationIds) {
			const corrIdColor = this.config.enableColors ? COLORS.magenta : "";
			output += `${corrIdColor}[${entry.correlationId.slice(0, 8)}]${reset} `;
		}

		// Message
		output += entry.message;

		// Data
		if (entry.data && Object.keys(entry.data).length > 0) {
			const dataStr = this.config.prettyPrint
				? JSON.stringify(entry.data, null, 2)
				: JSON.stringify(entry.data);
			output += `\n${dataStr}`;
		}

		// Error
		if (entry.error) {
			output += `\nError: ${entry.error.name}: ${entry.error.message}`;
			if (entry.error.stack && entry.level === LogLevel.ERROR) {
				output += `\n${entry.error.stack}`;
			}
		}

		return output;
	}

	/**
	 * Core logging method
	 */
	private log(
		level: LogLevel,
		message: string,
		data?: Record<string, unknown>,
		error?: Error,
	): void {
		// Check if log level is enabled
		if (level < this.config.level) {
			return;
		}

		const entry: LogEntry = {
			timestamp: new Date().toISOString(),
			level,
			message,
			data: data
				? (this.redactSensitiveData(data) as Record<string, unknown>)
				: undefined,
			correlationId: this.correlationId,
			context: this.context,
			error: error
				? {
						name: error.name,
						message: error.message,
						stack: error.stack,
					}
				: undefined,
		};

		const formattedMessage = this.formatLogEntry(entry);
		console.log(formattedMessage);
	}

	/**
	 * Debug level logging
	 */
	debug(message: string, data?: Record<string, unknown>): void {
		this.log(LogLevel.DEBUG, message, data);
	}

	/**
	 * Info level logging
	 */
	info(message: string, data?: Record<string, unknown>): void {
		this.log(LogLevel.INFO, message, data);
	}

	/**
	 * Warning level logging
	 */
	warn(message: string, data?: Record<string, unknown>, error?: Error): void {
		this.log(LogLevel.WARN, message, data, error);
	}

	/**
	 * Error level logging
	 */
	error(message: string, data?: Record<string, unknown>, error?: Error): void {
		this.log(LogLevel.ERROR, message, data, error);
	}

	/**
	 * Generate a new correlation ID
	 */
	static generateCorrelationId(): string {
		return randomUUID();
	}

	/**
	 * Create a timer for performance logging
	 */
	timer(label: string): () => void {
		const start = Date.now();
		return () => {
			const duration = Date.now() - start;
			this.info(`Timer: ${label}`, { duration: `${duration}ms` });
		};
	}
}

// Export singleton instance
export const logger = new Logger();

// Export class for creating custom instances
export { Logger };

// Export types
export type { LoggerConfig, LogEntry };
